package models

import "time"

type ProjectUsage struct {
	BaseModelHardDelete
	ProjectID      string     `json:"project_id" gorm:"column:project_id"`
	Amount         float64    `json:"amount" gorm:"column:amount"`
	HourCount      int        `json:"hour_count" gorm:"column:hour_count"`
	CycleID        string     `json:"cycle_id" gorm:"column:cycle_id"`
	OfficialAmount float64    `json:"official_amount" gorm:"column:official_amount"`
	TimeStamp      *time.Time `json:"timestamp" gorm:"column:timestamp"`
	Budget         *float64   `json:"budget" gorm:"column:budget"`

	// Relations
	Project *Project `json:"project,omitempty" gorm:"foreignKey:ProjectID"`
	Cycle   *Cycle   `json:"cycle,omitempty" gorm:"foreignKey:CycleID"`
}

func (ProjectUsage) TableName() string {
	return "project_usages"
}
