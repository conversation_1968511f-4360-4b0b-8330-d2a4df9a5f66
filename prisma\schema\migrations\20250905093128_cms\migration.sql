-- CreateEnum
CREATE TYPE "public"."CMSProjectStatus" AS ENUM ('DRAFT', 'NEW', 'IN_PROCESS', 'ACTIVE', 'CLOSE');

-- CreateEnum
CREATE TYPE "public"."CMSProjectType" AS ENUM ('NEW', 'MIGRATION');

-- CreateTable
CREATE TABLE "public"."cms_project_phases" (
    "id" UUID NOT NULL,
    "cms_project_id" UUID NOT NULL,
    "phase" INTEGER NOT NULL,
    "start_date" DATE NOT NULL,
    "end_date" DATE NOT NULL,
    "type" "public"."CMSProjectType" NOT NULL,
    "file_url" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "cms_project_phases_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."cms_projects" (
    "id" UUID NOT NULL,
    "ministry_id" UUID,
    "department_id" UUID,
    "division_id" UUID,
    "name" TEXT NOT NULL,
    "phase" INTEGER,
    "type" "public"."CMSProjectType" NOT NULL DEFAULT 'NEW',
    "domain" TEXT NOT NULL,
    "start_date" DATE,
    "end_date" DATE,
    "contact_name" TEXT NOT NULL,
    "contact_phone" TEXT NOT NULL,
    "contact_email" TEXT NOT NULL,
    "remark" TEXT NOT NULL,
    "status" "public"."CMSProjectStatus" NOT NULL DEFAULT 'DRAFT',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" TIMESTAMP(3),
    "created_by_id" UUID,
    "deleted_by_id" UUID,
    "updated_by_id" UUID,

    CONSTRAINT "cms_projects_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "cms_projects_name_key" ON "public"."cms_projects"("name");

-- CreateIndex
CREATE UNIQUE INDEX "cms_projects_domain_key" ON "public"."cms_projects"("domain");

-- CreateIndex
CREATE INDEX "cms_projects_ministry_id_department_id_division_id_name_con_idx" ON "public"."cms_projects"("ministry_id", "department_id", "division_id", "name", "contact_name", "contact_phone", "contact_email", "remark", "status");

-- AddForeignKey
ALTER TABLE "public"."cms_project_phases" ADD CONSTRAINT "cms_project_phases_cms_project_id_fkey" FOREIGN KEY ("cms_project_id") REFERENCES "public"."cms_projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;
