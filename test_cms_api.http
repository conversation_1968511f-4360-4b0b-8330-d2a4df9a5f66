### CMS Projects API Test File
### This file demonstrates the CMS module functionality

@base_url = http://localhost:3001
@auth_token = your_auth_token_here

### Get All CMS Projects (with pagination)
GET {{base_url}}/cms-projects?page=1&limit=10
Authorization: Bearer {{auth_token}}

### Get CMS Projects with Status Filter
GET {{base_url}}/cms-projects?status=DRAFT,NEW&page=1&limit=10
Authorization: Bearer {{auth_token}}

### Get CMS Projects with Type Filter
GET {{base_url}}/cms-projects?type=NEW,MIGRATION&page=1&limit=10
Authorization: Bearer {{auth_token}}

### Get CMS Projects with Ministry Filter
GET {{base_url}}/cms-projects?ministry_id=ministry-uuid-here&page=1&limit=10
Authorization: Bearer {{auth_token}}

### Get CMS Projects with Search
GET {{base_url}}/cms-projects?q=project&page=1&limit=10
Authorization: Bearer {{auth_token}}

### Get CMS Projects with Date Range
GET {{base_url}}/cms-projects?start_date=2024-01-01&end_date=2024-12-31&page=1&limit=10
Authorization: Bearer {{auth_token}}

### Get Single CMS Project
GET {{base_url}}/cms-projects/project-uuid-here
Authorization: Bearer {{auth_token}}

### Create New CMS Project
POST {{base_url}}/cms-projects
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
  "ministry_id": "ministry-uuid-here",
  "department_id": "department-uuid-here",
  "division_id": "division-uuid-here",
  "name": "Test CMS Project",
  "phase": 1,
  "type": "NEW",
  "domain": "test-cms.example.com",
  "start_date": "2024-01-01T00:00:00Z",
  "end_date": "2024-12-31T23:59:59Z",
  "contact_name": "John Doe",
  "contact_phone": "+66-123-456-789",
  "contact_email": "<EMAIL>",
  "remark": "This is a test CMS project",
  "status": "DRAFT"
}

### Update CMS Project
PUT {{base_url}}/cms-projects/project-uuid-here
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
  "name": "Updated CMS Project",
  "status": "NEW",
  "remark": "Updated project description"
}

### Update CMS Project Status Only
PATCH {{base_url}}/cms-projects/project-uuid-here/status
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
  "status": "ACTIVE"
}

### Delete CMS Project
DELETE {{base_url}}/cms-projects/project-uuid-here
Authorization: Bearer {{auth_token}}

### Get CMS Project Phases
GET {{base_url}}/cms-projects/project-uuid-here/phases?page=1&limit=10
Authorization: Bearer {{auth_token}}

### Get Single CMS Project Phase
GET {{base_url}}/cms-project-phases/phase-uuid-here
Authorization: Bearer {{auth_token}}

### Create New CMS Project Phase
POST {{base_url}}/cms-project-phases
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
  "cms_project_id": "project-uuid-here",
  "phase": 1,
  "start_date": "2024-01-01T00:00:00Z",
  "end_date": "2024-03-31T23:59:59Z",
  "type": "NEW",
  "file_url": "https://example.com/phase1-document.pdf"
}

### Update CMS Project Phase
PUT {{base_url}}/cms-project-phases/phase-uuid-here
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
  "phase": 2,
  "start_date": "2024-04-01T00:00:00Z",
  "end_date": "2024-06-30T23:59:59Z",
  "file_url": "https://example.com/phase2-document.pdf"
}

### Delete CMS Project Phase
DELETE {{base_url}}/cms-project-phases/phase-uuid-here
Authorization: Bearer {{auth_token}}

### Complex Query Example - Get CMS Projects with Multiple Filters
GET {{base_url}}/cms-projects?status=ACTIVE,IN_PROCESS&type=NEW&ministry_id=ministry-uuid&q=website&start_date=2024-01-01&page=1&limit=5&order_by=created_at DESC
Authorization: Bearer {{auth_token}}
