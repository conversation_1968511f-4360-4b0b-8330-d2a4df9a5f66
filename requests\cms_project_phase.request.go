package requests

import (
	"strconv"
	"time"

	"gitlab.finema.co/finema/csp/csp-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type CMSProjectPhaseCreate struct {
	core.BaseValidator
	Phase     *int64  `json:"phase"`
	WorkPhase *int64  `json:"work_phase"`
	StartDate *string `json:"start_date"`
	EndDate   *string `json:"end_date"`
	Type      *string `json:"type"`
	FileURL   *string `json:"file_url"`
}

func (r *CMSProjectPhaseCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrRequired(r.Type, "type"))
	r.Must(r.IsRequired(r.Phase, "phase"))
	r.Must(r.IsRequired(r.WorkPhase, "work_phase"))
	r.Must(r.Is<PERSON>equired(r.StartDate, "start_date"))
	r.Must(r.IsRequired(r.EndDate, "end_date"))
	r.Must(r.IsDate(r.StartDate, "start_date"))
	r.Must(r.IsDate(r.EndDate, "end_date"))
	r.Must(r.IsURL(r.FileURL, "file_url"))

	// Validate type enum
	r.Must(r.IsStrIn(r.Type,
		string(models.CMSProjectTypeNew)+"|"+string(models.CMSProjectTypeMigration),
		"type"))

	phaseStr := strconv.FormatInt(utils.ToNonPointer(r.Phase), 10)
	r.Must(r.IsStrUnique(ctx, utils.ToPointer(phaseStr), models.CMSProjectPhase{}.TableName(), "phase", "", "phase"))

	// Validate date range: end_date must be after start_date
	if r.StartDate != nil && r.EndDate != nil {
		startDate, startErr := time.Parse(time.DateOnly, utils.ToNonPointer(r.StartDate))
		endDate, endErr := time.Parse(time.DateOnly, utils.ToNonPointer(r.EndDate))

		if startErr == nil && endErr == nil {
			if !endDate.After(startDate) {
				r.Must(r.IsDateAfter(r.EndDate, r.StartDate, "end_date"))
			}
		}
	}

	return r.Error()
}

type CMSProjectPhaseUpdate struct {
	core.BaseValidator
	Phase     *int64  `json:"phase"`
	WorkPhase *int64  `json:"work_phase"`
	StartDate *string `json:"start_date"`
	EndDate   *string `json:"end_date"`
	Type      *string `json:"type"`
	FileURL   *string `json:"file_url"`
}

func (r *CMSProjectPhaseUpdate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrRequired(r.Type, "type"))
	r.Must(r.IsRequired(r.Phase, "phase"))
	r.Must(r.IsRequired(r.WorkPhase, "work_phase"))
	r.Must(r.IsRequired(r.StartDate, "start_date"))
	r.Must(r.IsRequired(r.EndDate, "end_date"))
	r.Must(r.IsDate(r.StartDate, "start_date"))
	r.Must(r.IsDate(r.EndDate, "end_date"))
	r.Must(r.IsURL(r.FileURL, "file_url"))

	// Validate type enum
	r.Must(r.IsStrIn(r.Type,
		string(models.CMSProjectTypeNew)+"|"+string(models.CMSProjectTypeMigration),
		"type"))

	// Validate date range: end_date must be after start_date
	if r.StartDate != nil && r.EndDate != nil {
		startDate, startErr := time.Parse(time.DateOnly, utils.ToNonPointer(r.StartDate))
		endDate, endErr := time.Parse(time.DateOnly, utils.ToNonPointer(r.EndDate))

		if startErr == nil && endErr == nil {
			if !endDate.After(startDate) {
				r.Must(r.IsDateAfter(r.EndDate, r.StartDate, "end_date"))
			}
		}
	}

	return r.Error()
}
