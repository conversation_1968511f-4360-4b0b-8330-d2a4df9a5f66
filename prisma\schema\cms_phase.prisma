model cms_project_phases {
  id             String         @id @default(uuid()) @db.Uuid
  cms_project_id String         @db.Uuid
  phase          Int
  work_phase     Int
  start_date     DateTime       @db.Date
  end_date       DateTime       @db.Date
  type           CMSProjectType
  file_url       String
  created_at     DateTime       @default(now())
  updated_at     DateTime       @default(now()) @updatedAt

  project cms_projects @relation(fields: [cms_project_id], references: [id], onDelete: Cascade)

  @@index([cms_project_id, phase, work_phase, start_date, end_date, type])
}
