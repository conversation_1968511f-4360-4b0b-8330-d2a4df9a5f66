package services

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
)

type CMSProjectCreatePayload struct {
	MinistryID   string
	DepartmentID string
	DivisionID   string
	Name         string
	Type         string
	Domain       string
	ContactName  string
	ContactPhone string
	ContactEmail string
	Remark       string
	Status       string
	Phase        *struct {
		Phase     int64
		WorkPhase int64
		StartDate string
		EndDate   string
		FileURL   string
	}
}

type CMSProjectUpdatePayload struct {
	MinistryID   string
	DepartmentID string
	DivisionID   string
	Name         string
	Type         string
	Domain       string
	ContactName  string
	ContactPhone string
	ContactEmail string
	Remark       string
	Status       string
}

type CMSProjectFilters struct {
	Status       []string
	Type         []string
	MinistryID   string
	DepartmentID string
	DivisionID   string
	StartDate    string
	EndDate      string
}

type CMSProjectPhaseCreatePayload struct {
	CMSProjectID string
	Phase        int64
	WorkPhase    int64
	StartDate    string
	EndDate      string
	Type         string
	FileURL      string
}

type CMSProjectPhaseUpdatePayload struct {
	Phase     int64
	WorkPhase int64
	StartDate string
	EndDate   string
	Type      string
	FileURL   string
}

// CMS Dashboard Response Structures
type CMSDashboardResponse struct {
	ProjectStats          *CMSProjectStats    `json:"project_stats"`
	LatestProjects        []models.CMSProject `json:"latest_projects"`
	NearlyExpiredProjects []models.CMSProject `json:"nearly_expired_projects"`
}

type CMSProjectStats struct {
	Total         int64 `json:"total"`
	Draft         int64 `json:"draft"`
	New           int64 `json:"new"`
	InProcess     int64 `json:"in_process"`
	Active        int64 `json:"active"`
	Close         int64 `json:"close"`
	NearlyExpired int64 `json:"nearly_expired"`
}

type CMSStatusStat struct {
	Status  models.CMSProjectStatus `json:"status"`
	Count   int64                   `json:"count"`
	Percent float64                 `json:"percent"`
}
