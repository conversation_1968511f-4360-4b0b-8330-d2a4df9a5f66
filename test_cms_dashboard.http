### CMS Dashboard API Test
### Test the CMS dashboard endpoint

# Set base URL
@baseUrl = http://localhost:8080
@authToken = your_auth_token_here

### Get CMS Dashboard Data
GET {{baseUrl}}/cms/dashboard
Authorization: Bearer {{authToken}}
Content-Type: application/json

###

# Expected Response Structure:
# {
#   "project_stats": {
#     "total": 0,
#     "draft": 0,
#     "new": 0,
#     "in_process": 0,
#     "active": 0,
#     "close": 0
#   },
#   "latest_projects": [],
#   "projects_by_status": [
#     {
#       "status": "DRAFT",
#       "count": 0,
#       "percent": 0
#     },
#     {
#       "status": "NEW", 
#       "count": 0,
#       "percent": 0
#     },
#     {
#       "status": "IN_PROCESS",
#       "count": 0,
#       "percent": 0
#     },
#     {
#       "status": "ACTIVE",
#       "count": 0,
#       "percent": 0
#     },
#     {
#       "status": "CLOSE",
#       "count": 0,
#       "percent": 0
#     }
#   ],
#   "nearly_expired_projects": []
# }
