package services

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	"gitlab.finema.co/finema/csp/csp-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

type ICycleService interface {
	Find(id string) (*models.Cycle, core.IError)
	Pagination(filters *CycleFilters, pageOptions *core.PageOptions) (*repository.Pagination[models.Cycle], core.IError)
	FindCurrent() (*models.Cycle, core.IError)
}

type cycleService struct {
	ctx core.IContext
}

type CycleFilters struct {
	Status    []string
	StartDate string
	EndDate   string
}

func (s cycleService) Find(id string) (*models.Cycle, core.IError) {
	return repo.Cycle(s.ctx).FindOne("id = ?", id)
}

func (s cycleService) Pagination(filters *CycleFilters, pageOptions *core.PageOptions) (*repository.Pagination[models.Cycle], core.IError) {
	return repo.Cycle(
		s.ctx,
		repo.CycleOrderBy(pageOptions),
		repo.CycleByStatus(filters.Status),
		repo.CycleByDateRange(filters.StartDate, filters.EndDate),
		repo.CycleBySearch(pageOptions.Q)).
		Pagination(pageOptions)
}

func (s cycleService) FindCurrent() (*models.Cycle, core.IError) {
	return repo.Cycle(s.ctx, repo.CycleCurrent()).FindOne()
}

func NewCycleService(ctx core.IContext) ICycleService {
	return &cycleService{ctx: ctx}
}
