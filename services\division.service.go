package services

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	"gitlab.finema.co/finema/csp/csp-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type IDivisionService interface {
	Create(input *DivisionCreatePayload) (*models.Division, core.IError)
	Update(id string, input *DivisionUpdatePayload) (*models.Division, core.IError)
	Find(id string) (*models.Division, core.IError)
	Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.Division], core.IError)
	Delete(id string) core.IError
	FindByMinistry(ministryID string, pageOptions *core.PageOptions) (*repository.Pagination[models.Division], core.IError)
	FindByDepartment(departmentID string, pageOptions *core.PageOptions) (*repository.Pagination[models.Division], core.IError)
}

type divisionService struct {
	ctx core.IContext
}

func (s divisionService) Create(input *DivisionCreatePayload) (*models.Division, core.IError) {
	division := &models.Division{
		BaseModelHardDelete: models.NewBaseModelHardDelete(),
		NameTh:              input.NameTh,
		NameEn:              utils.ToPointer(input.NameEn),
		Code:                input.Code,
		ShortNameTh:         input.ShortNameTh,
		ShortNameEn:         input.ShortNameEn,
	}

	ierr := repo.Division(s.ctx).Create(division)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(division.ID)
}

func (s divisionService) Update(id string, input *DivisionUpdatePayload) (*models.Division, core.IError) {
	division, ierr := s.Find(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	if input.NameTh != "" {
		division.NameTh = input.NameTh
	}
	if input.NameEn != "" {
		division.NameEn = utils.ToPointer(input.NameEn)
	}
	if input.Code != nil {
		division.Code = input.Code
	}
	if input.ShortNameTh != nil {
		division.ShortNameTh = input.ShortNameTh
	}
	if input.ShortNameEn != nil {
		division.ShortNameEn = input.ShortNameEn
	}

	// Update timestamp
	division.UpdatedAt = utils.GetCurrentDateTime()

	ierr = repo.Division(s.ctx).Where("id = ?", id).Updates(division)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(division.ID)
}

func (s divisionService) Find(id string) (*models.Division, core.IError) {
	return repo.Division(s.ctx).FindOne("id = ?", id)
}

func (s divisionService) Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.Division], core.IError) {
	return repo.Division(
		s.ctx,
		repo.DivisionOrderBy(pageOptions),
		repo.DivisionWithSearch(pageOptions.Q)).
		Pagination(pageOptions)
}

func (s divisionService) FindByMinistry(ministryID string, pageOptions *core.PageOptions) (*repository.Pagination[models.Division], core.IError) {
	return repo.Division(s.ctx, repo.DivisionByMinistry(ministryID), repo.DivisionOrderBy(pageOptions)).Pagination(pageOptions)
}

func (s divisionService) FindByDepartment(departmentID string, pageOptions *core.PageOptions) (*repository.Pagination[models.Division], core.IError) {
	return repo.Division(s.ctx, repo.DivisionByDepartment(departmentID), repo.DivisionOrderBy(pageOptions)).Pagination(pageOptions)
}

func (s divisionService) Delete(id string) core.IError {
	_, ierr := s.Find(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repo.Division(s.ctx).Delete("id = ?", id)
}

func NewDivisionService(ctx core.IContext) IDivisionService {
	return &divisionService{ctx: ctx}
}
