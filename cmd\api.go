package cmd

import (
	"fmt"
	"os"

	"gitlab.finema.co/finema/csp/csp-api/modules/auth"
	"gitlab.finema.co/finema/csp/csp-api/modules/cms"
	"gitlab.finema.co/finema/csp/csp-api/modules/cycle"
	"gitlab.finema.co/finema/csp/csp-api/modules/dashboard"
	"gitlab.finema.co/finema/csp/csp-api/modules/department"
	"gitlab.finema.co/finema/csp/csp-api/modules/division"
	"gitlab.finema.co/finema/csp/csp-api/modules/home"
	"gitlab.finema.co/finema/csp/csp-api/modules/me"
	"gitlab.finema.co/finema/csp/csp-api/modules/ministry"
	"gitlab.finema.co/finema/csp/csp-api/modules/organization"
	"gitlab.finema.co/finema/csp/csp-api/modules/project"
	"gitlab.finema.co/finema/csp/csp-api/modules/projectusage"
	"gitlab.finema.co/finema/csp/csp-api/modules/user"
	core "gitlab.finema.co/finema/idin-core"
)

func APIRun() {
	env := core.NewEnv()
	db, err := core.NewDatabase(env.Config()).Connect()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Postgres: %v \n", err)
		os.Exit(1)
	}

	e := core.NewHTTPServer(&core.HTTPContextOptions{
		ContextOptions: &core.ContextOptions{
			DB:  db,
			ENV: env,
		},
	})

	//e.Pre(middleware.Rewrite(map[string]string{
	//	"/api/*": "/$1",
	//}))

	home.NewHomeHTTP(e)
	dashboard.NewDashboardHTTP(e)

	// Register modules
	auth.NewAuthHTTP(e)
	me.NewMeHTTP(e)
	user.NewUserHTTP(e)

	// Register new CRUD modules
	ministry.NewMinistryHTTP(e)
	department.NewDepartmentHTTP(e)
	division.NewDivisionHTTP(e)
	organization.NewOrganizationHTTP(e)
	project.NewProjectHTTP(e)
	projectusage.NewProjectUsageHTTP(e)
	cycle.NewCycleHTTP(e)
	cms.NewCMSHTTP(e)

	core.StartHTTPServer(e, env)
}
