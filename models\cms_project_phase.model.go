package models

import "time"

type CMSProjectPhase struct {
	BaseModelHardDelete
	CMSProjectID string         `json:"cms_project_id" gorm:"column:cms_project_id"`
	Phase        int64          `json:"phase" gorm:"column:phase"`
	WorkPhase    int64          `json:"work_phase" gorm:"column:work_phase"`
	StartDate    *time.Time     `json:"start_date" gorm:"column:start_date"`
	EndDate      *time.Time     `json:"end_date" gorm:"column:end_date"`
	Type         CMSProjectType `json:"type" gorm:"column:type"`
	FileURL      string         `json:"file_url" gorm:"column:file_url"`
	// Relations
	Project *CMSProject `json:"project,omitempty" gorm:"foreignKey:CMSProjectID"`
}

func (CMSProjectPhase) TableName() string {
	return "cms_project_phases"
}
