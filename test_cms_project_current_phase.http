### CMS Project Current Phase Test File
### This file demonstrates testing the CMSProjectWithCurrentPhase functionality

@base_url = http://localhost:3001
@auth_token = your_auth_token_here

### Test 1: Get a single CMS Project with current phase
### This should return the project with the current_phase field populated
GET {{base_url}}/cms-projects/project-uuid-here
Authorization: Bearer {{auth_token}}

### Test 2: Get all CMS Projects with current phase (pagination)
### This should return all projects with their current_phase values
GET {{base_url}}/cms-projects?page=1&limit=10
Authorization: Bearer {{auth_token}}

### Test 3: Get CMS Projects with filters and current phase
### This should return filtered projects with their current_phase values
GET {{base_url}}/cms-projects?status=ACTIVE&type=NEW&page=1&limit=5
Authorization: Bearer {{auth_token}}

### Test 4: Create a test project first (for testing purposes)
POST {{base_url}}/cms-projects
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
  "ministry_id": "ministry-uuid-here",
  "department_id": "department-uuid-here", 
  "division_id": "division-uuid-here",
  "name": "Test Project for Current Phase",
  "type": "NEW",
  "domain": "test-current-phase.example.com",
  "start_date": "2024-01-01",
  "end_date": "2024-12-31",
  "contact_name": "Test Contact",
  "contact_phone": "0123456789",
  "contact_email": "<EMAIL>",
  "remark": "Test project for current phase functionality",
  "status": "DRAFT"
}

### Test 5: Create phases for the test project
POST {{base_url}}/cms-projects/project-uuid-here/phases
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
  "phase": 1,
  "work_phase": 1,
  "start_date": "2024-01-01",
  "end_date": "2024-03-31",
  "type": "NEW",
  "file_url": "https://example.com/phase1.pdf"
}

### Test 6: Create second phase
POST {{base_url}}/cms-projects/project-uuid-here/phases
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
  "phase": 2,
  "work_phase": 2,
  "start_date": "2024-04-01",
  "end_date": "2024-06-30",
  "type": "NEW",
  "file_url": "https://example.com/phase2.pdf"
}

### Test 7: Create third phase (this should be the current/latest phase)
POST {{base_url}}/cms-projects/project-uuid-here/phases
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
  "phase": 3,
  "work_phase": 3,
  "start_date": "2024-07-01",
  "end_date": "2024-09-30",
  "type": "NEW",
  "file_url": "https://example.com/phase3.pdf"
}

### Test 8: Verify the project now shows current_phase = 3
GET {{base_url}}/cms-projects/project-uuid-here
Authorization: Bearer {{auth_token}}

### Expected Result:
### The response should include a "phase" object with the highest phase number (3)
### The "phase" field should contain the complete CMSProjectPhase record with phase: 3

### Test 9: Test with project that has no phases
### Create a project without any phases
POST {{base_url}}/cms-projects
Authorization: Bearer {{auth_token}}
Content-Type: application/json

{
  "ministry_id": "ministry-uuid-here",
  "department_id": "department-uuid-here",
  "division_id": "division-uuid-here", 
  "name": "Test Project No Phases",
  "type": "NEW",
  "domain": "test-no-phases.example.com",
  "start_date": "2024-01-01",
  "end_date": "2024-12-31",
  "contact_name": "Test Contact",
  "contact_phone": "0123456789", 
  "contact_email": "<EMAIL>",
  "remark": "Test project with no phases",
  "status": "DRAFT"
}

### Test 10: Get the project with no phases
GET {{base_url}}/cms-projects/project-no-phases-uuid-here
Authorization: Bearer {{auth_token}}

### Expected Result:
### The response should include "phase": null
### since there are no phases for this project

### Notes for Testing:
### 1. Replace "project-uuid-here" with actual project UUIDs from your database
### 2. Replace "ministry-uuid-here", "department-uuid-here", "division-uuid-here" with actual UUIDs
### 3. Replace "your_auth_token_here" with a valid authentication token
### 4. The "phase" field should appear in the JSON response for projects
### 5. Projects with phases should show the complete phase object with the highest phase number
### 6. Projects without phases should show "phase": null
