package cms

import (
	"net/http"

	"gitlab.finema.co/finema/csp/csp-api/requests"
	"gitlab.finema.co/finema/csp/csp-api/services"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type CMSProjectPhaseController struct {
}

func (m CMSProjectPhaseController) FindByProject(c core.IHTTPContext) error {
	cmsProjectPhaseSvc := services.NewCMSProjectPhaseService(c)
	res, ierr := cmsProjectPhaseSvc.FindByProject(c.Param("project_id"), c.GetPageOptions())
	if ierr != nil {
		return c.JSON(ierr.GetStatus(), ierr.JSON())
	}

	return c.JSON(http.StatusOK, res)
}

func (m CMSProjectPhaseController) Find(c core.IHTTPContext) error {
	cmsProjectPhaseSvc := services.NewCMSProjectPhaseService(c)
	cmsProjectPhase, err := cmsProjectPhaseSvc.Find(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, cmsProjectPhase)
}

func (m CMSProjectPhaseController) Create(c core.IHTTPContext) error {
	input := &requests.CMSProjectPhaseCreate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	cmsProjectPhaseSvc := services.NewCMSProjectPhaseService(c)
	payload := &services.CMSProjectPhaseCreatePayload{}
	_ = utils.Copy(payload, input)
	payload.CMSProjectID = c.Param("project_id")
	cmsProjectPhase, err := cmsProjectPhaseSvc.Create(payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusCreated, cmsProjectPhase)
}

func (m CMSProjectPhaseController) Update(c core.IHTTPContext) error {
	input := &requests.CMSProjectPhaseUpdate{}
	if err := c.BindWithValidate(input); err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	cmsProjectPhaseSvc := services.NewCMSProjectPhaseService(c)
	payload := &services.CMSProjectPhaseUpdatePayload{}
	_ = utils.Copy(payload, input)
	cmsProjectPhase, err := cmsProjectPhaseSvc.Update(c.Param("id"), payload)
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.JSON(http.StatusOK, cmsProjectPhase)
}

func (m CMSProjectPhaseController) Delete(c core.IHTTPContext) error {
	cmsProjectPhaseSvc := services.NewCMSProjectPhaseService(c)
	err := cmsProjectPhaseSvc.Delete(c.Param("id"))
	if err != nil {
		return c.JSON(err.GetStatus(), err.JSON())
	}

	return c.NoContent(http.StatusNoContent)
}
