### CMS Project Phase Date Comparison API Tests
### Test the CMS project phase date comparison functionality

# Set base URL and auth token
@baseUrl = http://localhost:8080
@authToken = your_auth_token_here
@projectId = your_project_id_here

### 1. Create a CMS Project Phase with valid date range
POST {{baseUrl}}/cms/projects/{{projectId}}/phases
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "phase": 1,
  "work_phase": 1,
  "start_date": "2024-01-01",
  "end_date": "2024-03-31",
  "type": "NEW",
  "file_url": "https://example.com/phase1.pdf"
}

### 2. Try to create a phase with invalid date range (end_date before start_date)
POST {{baseUrl}}/cms/projects/{{projectId}}/phases
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "phase": 2,
  "work_phase": 2,
  "start_date": "2024-03-31",
  "end_date": "2024-01-01",
  "type": "NEW",
  "file_url": "https://example.com/phase2.pdf"
}

### 3. Get current phases (phases that are running now)
GET {{baseUrl}}/cms/phases/current
Authorization: Bearer {{authToken}}

### 4. Get upcoming phases (phases that haven't started yet)
GET {{baseUrl}}/cms/phases/upcoming
Authorization: Bearer {{authToken}}

### 5. Get completed phases (phases that have ended)
GET {{baseUrl}}/cms/phases/completed
Authorization: Bearer {{authToken}}

### 6. Get phases by date range
GET {{baseUrl}}/cms/phases/date-range?start_date=2024-01-01&end_date=2024-12-31
Authorization: Bearer {{authToken}}

### 7. Get overlapping phases for a specific project
GET {{baseUrl}}/cms/projects/{{projectId}}/phases/overlapping
Authorization: Bearer {{authToken}}

### 8. Get phases by date range with invalid date format (should return error)
GET {{baseUrl}}/cms/phases/date-range?start_date=invalid-date&end_date=2024-12-31
Authorization: Bearer {{authToken}}

### 9. Get phases by date range without required parameters (should return error)
GET {{baseUrl}}/cms/phases/date-range
Authorization: Bearer {{authToken}}

### 10. Update a phase with valid date range
PUT {{baseUrl}}/cms/projects/{{projectId}}/phases/phase_id_here
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "phase": 1,
  "work_phase": 1,
  "start_date": "2024-02-01",
  "end_date": "2024-04-30",
  "type": "NEW",
  "file_url": "https://example.com/phase1-updated.pdf"
}

### 11. Try to update a phase with invalid date range
PUT {{baseUrl}}/cms/projects/{{projectId}}/phases/phase_id_here
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "phase": 1,
  "work_phase": 1,
  "start_date": "2024-04-30",
  "end_date": "2024-02-01",
  "type": "NEW",
  "file_url": "https://example.com/phase1-invalid.pdf"
}

###

# Expected Response Examples:

# Current Phases Response:
# [
#   {
#     "id": "uuid",
#     "phase": 1,
#     "start_date": "2024-01-01T00:00:00Z",
#     "end_date": "2024-03-31T00:00:00Z",
#     "type": "NEW",
#     "project": {...}
#   }
# ]

# Date Range Validation Error Response:
# {
#   "status": 400,
#   "code": "VALIDATION_ERROR",
#   "message": "Validation failed",
#   "errors": [
#     {
#       "field": "end_date",
#       "message": "End date must be after start date"
#     }
#   ]
# }

# Date Format Error Response:
# {
#   "error": "Invalid start_date format. Use YYYY-MM-DD"
# }

# Missing Parameters Error Response:
# {
#   "error": "start_date and end_date query parameters are required"
# }
