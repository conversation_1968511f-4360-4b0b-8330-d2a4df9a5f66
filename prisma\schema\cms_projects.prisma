enum CMSProjectStatus {
  DRAFT
  NEW
  IN_PROCESS
  ACTIVE
  CLOSE
}

enum CMSProjectType {
  NEW
  MIGRATION
}

model cms_projects {
  id            String           @id @default(uuid()) @db.Uuid
  ministry_id   String?          @db.Uuid
  department_id String?          @db.Uuid
  division_id   String?          @db.Uuid
  phase_id      String?          @db.Uuid
  name          String           @unique
  type          CMSProjectType   @default(NEW)
  domain        String           @unique
  contact_name  String
  contact_phone String
  contact_email String
  remark        String
  status        CMSProjectStatus @default(DRAFT)

  created_at    DateTime  @default(now())
  updated_at    DateTime  @default(now()) @updatedAt
  deleted_at    DateTime?
  created_by_id String?   @db.Uuid
  deleted_by_id String?   @db.Uuid
  updated_by_id String?   @db.Uuid

  phases cms_project_phases[]

  @@index([ministry_id, department_id, division_id, name, contact_name, contact_phone, contact_email, remark, status])
}
